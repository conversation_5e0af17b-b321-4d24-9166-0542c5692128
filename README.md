# AP World History - Must Know Dates Practice

An interactive web application for AP World History students to practice matching historical events with their corresponding dates. Features a clean, accessible design with period-based filtering and engaging feedback systems.

## Features

- **Period-Based Learning**: Filter content by AP World History periods (0-4)
- **Visual Connection Lines**: Animated curved lines connect matched items with color-coded feedback
- **Interactive Matching**: Drag-and-drop or click-to-connect interface
- **Smart Auto-Retry**: Automatically continues when all submitted matches are correct but items remain unmatched
- **Hide Correct Matches**: Toggle switch to hide correct matches and focus on remaining items
- **Enhanced Feedback**: Red boxes with X marks, shake animations, and visual pulses for incorrect matches
- **Immediate Feedback**: Color-coded responses (green for correct, red for incorrect)
- **Retry System**: Focus on incorrect matches without losing progress
- **Celebratory Completion**: Mint green confetti animation upon success
- **Accessibility**: Full keyboard navigation and screen reader support
- **Mobile-Friendly**: Responsive design optimized for all devices

## How to Use

1. Select a historical period from the dropdown menu
2. **Optional**: Toggle "Hide correct matches" to automatically hide correct answers after each submission
3. Match dates with their corresponding events by:
   - **Desktop**: Drag and drop items between columns
   - **Mobile/Touch**: Tap a date, then tap its matching event
   - **Keyboard**: Use Tab to navigate, Enter/Space to select, Escape to clear
4. Watch as **animated connection lines** appear between your matched items
5. Click "Submit Answers" to check your matches
6. **Smart Auto-Retry**: If all your matches are correct but some items remain, the game automatically continues
7. Review feedback with **enhanced visual cues** (red boxes with X marks for incorrect matches)
8. Retry incorrect matches or continue with remaining items
9. Celebrate when you get them all right! 🎉

## Historical Periods Covered

- **Period 0**: 8000 B.C.E - 1200 C.E (Review)
- **Period 1**: c. 1200 C.E. to c. 1450 C.E.
- **Period 2**: c. 1450 C.E. to c. 1750 C.E.
- **Period 3**: c. 1750 C.E. to c. 1900 C.E.
- **Period 4**: c. 1900 C.E. to the present

## Technical Details

- **Frontend**: Vanilla HTML, CSS, and JavaScript
- **Styling**: Modern CSS with CSS Grid and Flexbox
- **Accessibility**: WCAG 2.1 compliant with ARIA labels
- **Performance**: Optimized for fast loading and smooth interactions
- **Hosting**: GitHub Pages ready

## Local Development

1. Clone the repository
2. Open `index.html` in a web browser, or
3. Run a local server: `python3 -m http.server 8000`
4. Navigate to `http://localhost:8000`

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Contributing

This project is designed for educational use. Feel free to fork and adapt for your own teaching needs.

## License

MIT License - feel free to use this for educational purposes.